package com.zayed.springrest.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zayed.springrest.model.Voucher;
import com.zayed.springrest.repos.VoucherRepository;

@RestController
@RequestMapping("/voucherapi")
public class VoucherController {

	@Autowired
	VoucherRepository repository;
	@PostMapping("/vouchers")
	public Voucher create(@RequestBody Voucher voucher) {
		return repository.save(voucher);
	}
	
	@GetMapping("/vouchers/{code}")
	public Voucher getVoucher(@PathVariable("code") String code) {
		return repository.findByCode(code);
	}
	
}
